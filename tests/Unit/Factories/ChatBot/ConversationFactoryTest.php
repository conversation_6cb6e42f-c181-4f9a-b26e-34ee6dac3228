<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Domains\ChatBot\Conversation;
use App\Factories\ChatBot\ConversationFactory;
use App\Models\Conversation as ConversationModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class ConversationFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(ConversationFactory::class);

        $this->assertInstanceOf(ConversationFactory::class, $factory);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->flow_id, $domain->flow_id);
        $this->assertEquals($model->json, $domain->json);
        $this->assertEquals($model->is_finished, $domain->is_finished);
    }

    protected function createFactoryInstance()
    {
        return app()->make(ConversationFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Conversation::class;
    }

    protected function createModelInstance()
    {
        return new ConversationModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'phone_number_id' => 1,
            'current_step_id' => 1,
            'json' => json_encode(['test' => 'data']),
            'is_finished' => false,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }
}
