<?php

namespace Tests\Unit\Factories\ChatBot;

use Tests\TestCase;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Domains\ChatBot\ExchangedMessage;
use App\Models\ExchangedMessage as ExchangedMessageModel;
use App\Models\Organization;
use App\Models\Client;
use App\Models\PhoneNumber;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\WhatsAppWebhookLog;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ExchangedMessageFactoryTest extends TestCase
{
    use RefreshDatabase;

    private ExchangedMessageFactory $factory;
    private Organization $organization;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->factory = app()->make(ExchangedMessageFactory::class);
        
        $this->organization = Organization::factory()->create();
        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);
    }

    public function test_factory_can_be_instantiated()
    {
        $this->assertInstanceOf(ExchangedMessageFactory::class, $this->factory);
    }

    public function test_can_build_from_model()
    {
        // Create a model
        $model = ExchangedMessageModel::create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'inbound' => true,
            'outbound' => false,
            'message' => 'Test message',
            'json' => ['test' => 'data'],
            'sent_at' => Carbon::now(),
        ]);

        // Build domain from model
        $domain = $this->factory->buildFromModel($model);

        // Assert
        $this->assertInstanceOf(ExchangedMessage::class, $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->inbound, $domain->inbound);
        $this->assertEquals($model->outbound, $domain->outbound);
        $this->assertEquals($model->message, $domain->message);
        $this->assertEquals($model->json, $domain->json);
    }

    public function test_can_build_from_model_with_null()
    {
        $domain = $this->factory->buildFromModel(null);
        $this->assertNull($domain);
    }

    public function test_domain_to_array_works()
    {
        $domain = new ExchangedMessage(
            id: 1,
            organization_id: $this->organization->id,
            client_id: $this->client->id,
            inbound: true,
            outbound: false,
            message: 'Test message',
            json: ['test' => 'data'],
            sent_at: Carbon::now()
        );

        $array = $domain->toArray();

        $this->assertIsArray($array);
        $this->assertEquals(1, $array['id']);
        $this->assertEquals($this->organization->id, $array['organization_id']);
        $this->assertEquals($this->client->id, $array['client_id']);
        $this->assertTrue($array['inbound']);
        $this->assertFalse($array['outbound']);
        $this->assertEquals('Test message', $array['message']);
        $this->assertEquals(['test' => 'data'], $array['json']);
    }

    public function test_domain_direction_methods()
    {
        $inboundMessage = new ExchangedMessage(inbound: true, outbound: false);
        $outboundMessage = new ExchangedMessage(inbound: false, outbound: true);
        $unknownMessage = new ExchangedMessage(inbound: false, outbound: false);

        $this->assertTrue($inboundMessage->isInbound());
        $this->assertFalse($inboundMessage->isOutbound());
        $this->assertEquals('inbound', $inboundMessage->getDirection());

        $this->assertFalse($outboundMessage->isInbound());
        $this->assertTrue($outboundMessage->isOutbound());
        $this->assertEquals('outbound', $outboundMessage->getDirection());

        $this->assertFalse($unknownMessage->isInbound());
        $this->assertFalse($unknownMessage->isOutbound());
        $this->assertEquals('unknown', $unknownMessage->getDirection());
    }
}
