<?php

namespace Tests\Unit\Factories\ChatBot;

use App\Domains\ChatBot\Interaction;
use App\Factories\ChatBot\InteractionFactory;
use App\Factories\ChatBot\FlowFactory;
use App\Factories\ChatBot\StepFactory;
use App\Factories\Inventory\ClientFactory;
use App\Factories\UserFactory;
use App\Models\Interaction as InteractionModel;
use Tests\Unit\Factories\BaseFactoryTest;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class InteractionFactoryTest extends BaseFactoryTest
{
    use RefreshDatabase;

    public function test_app_make_resolves_factory_correctly()
    {
        $factory = app()->make(InteractionFactory::class);

        $this->assertInstanceOf(InteractionFactory::class, $factory);
    }

    public function test_build_from_model()
    {
        $model = $this->createModelInstance();
        $factory = $this->createFactoryInstance();
        $domain = $factory->buildFromModel($model);

        $this->assertInstanceOf($this->getDomainClass(), $domain);
        $this->assertEquals($model->id, $domain->id);
        $this->assertEquals($model->organization_id, $domain->organization_id);
        $this->assertEquals($model->user_id, $domain->user_id);
        $this->assertEquals($model->client_id, $domain->client_id);
        $this->assertEquals($model->flow_id, $domain->flow_id);
        $this->assertEquals($model->step_id, $domain->step_id);
        $this->assertEquals($model->conversation_id, $domain->conversation_id);
        $this->assertEquals($model->message, $domain->message);
        $this->assertEquals($model->answer, $domain->answer);
        $this->assertEquals($model->result, $domain->result);
        $this->assertEquals($model->json, $domain->json);
    }

    protected function createFactoryInstance()
    {
        return app()->make(InteractionFactory::class);
    }

    protected function getDomainClass(): string
    {
        return Interaction::class;
    }

    protected function createModelInstance()
    {
        return new InteractionModel([
            'id' => 1,
            'organization_id' => 1,
            'user_id' => 1,
            'client_id' => 1,
            'flow_id' => 1,
            'step_id' => 1,
            'conversation_id' => 1,
            'message' => 'Test message',
            'answer' => 'Test answer',
            'result' => 'success',
            'json' => json_encode(['test' => 'data']),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ]);
    }
}
