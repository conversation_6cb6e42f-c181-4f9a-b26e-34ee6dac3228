<?php

namespace Tests\Unit\Factories\ChatBot;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Factories\ChatBot\MessageFactory;
use App\Factories\Inventory\ClientFactory;
use App\Factories\ChatBot\TemplateFactory;
use App\Http\Requests\WhatsApp\SendRealTimeMessageRequest;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\Template;
use App\Domains\Inventory\Client;
use App\Models\User;
use App\Models\Organization;
use App\Enums\MessageStatus;

class MessageFactoryTest extends TestCase
{
    use RefreshDatabase;

    private MessageFactory $factory;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->actingAs($this->user);

        $clientFactory = $this->createMock(ClientFactory::class);
        $templateFactory = $this->createMock(TemplateFactory::class);

        $this->factory = new MessageFactory($clientFactory, $templateFactory);
    }

    private function createTestClient(int $id = 1): Client
    {
        return new Client(
            id: $id,
            organization_id: $this->organization->id,
            name: 'John Doe',
            phone: '+5511999999999',
            whatsapp_from: null,
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );
    }

    private function createTestTemplate(int $id = 5): Template
    {
        return new Template(
            id: $id,
            organization_id: $this->organization->id,
            phone_number_id: null,
            user_id: null,
            client_id: null,
            name: 'Test Template',
            category: null,
            parameter_format: null,
            language: null,
            library_template_name: null,
            id_external: null,
            status: null
        );
    }

    public function test_can_build_from_send_individual_message_without_template()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello {{client.name}}!',
            'client_id' => 1,
            'phone_number_id' => 1,
            'template_id' => null,
            'is_direct_message' => true
        ]);

        $client = $this->createTestClient();

        // Act
        $message = $this->factory->buildFromSendIndividualMessage($request, $client, null);

        // Assert
        $this->assertInstanceOf(Message::class, $message);
        $this->assertNull($message->id);
        $this->assertEquals($this->organization->id, $message->organization_id);
        $this->assertNull($message->campaign_id); // ✅ Critical: campaign_id must be null
        $this->assertNull($message->template_id);
        $this->assertEquals(1, $message->client_id);
        $this->assertEquals('Hello {{client.name}}!', $message->message);
        $this->assertEquals(MessageStatus::is_sending, $message->status);
        $this->assertFalse($message->is_sent);
        $this->assertFalse($message->is_delivered);
        $this->assertFalse($message->is_fail);
        $this->assertFalse($message->is_read);
        $this->assertTrue($message->is_direct_message);
        $this->assertEquals(0, $message->delivery_attempts);
        $this->assertNull($message->last_attempt_at);
        $this->assertEquals(3, $message->max_retries);
        $this->assertNull($message->next_retry_at);
        $this->assertNull($message->last_error_message);
        $this->assertNull($message->sent_at);
        $this->assertNull($message->scheduled_at);
        $this->assertNull($message->created_at);
        $this->assertNull($message->updated_at);
        $this->assertEquals($client, $message->client);
        $this->assertNull($message->template);
        $this->assertNull($message->campaign);
        $this->assertNull($message->delivery_attempts_history);
    }

    public function test_can_build_from_send_individual_message_with_template()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Template message',
            'client_id' => 1,
            'phone_number_id' => 1,
            'template_id' => 5,
            'is_direct_message' => false
        ]);

        $client = $this->createTestClient();

        $template = $this->createTestTemplate();

        // Act
        $message = $this->factory->buildFromSendIndividualMessage($request, $client, $template);

        // Assert
        $this->assertInstanceOf(Message::class, $message);
        $this->assertNull($message->campaign_id); // ✅ Critical: campaign_id must be null
        $this->assertEquals(5, $message->template_id);
        $this->assertEquals('Template message', $message->message);
        $this->assertFalse($message->is_direct_message); // Explicitly set to false
        $this->assertEquals($client, $message->client);
        $this->assertEquals($template, $message->template);
    }

    public function test_defaults_is_direct_message_to_true_when_not_provided()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello!',
            'client_id' => 1,
            'phone_number_id' => 1
            // is_direct_message not provided
        ]);

        $client = $this->createTestClient();

        // Act
        $message = $this->factory->buildFromSendIndividualMessage($request, $client, null);

        // Assert
        $this->assertTrue($message->is_direct_message); // Should default to true
    }

    public function test_campaign_id_is_always_null_for_realtime_messages()
    {
        // This is a critical test to ensure real-time messages are never associated with campaigns

        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello!',
            'client_id' => 1,
            'phone_number_id' => 1
        ]);

        $client = $this->createTestClient();

        // Act
        $message = $this->factory->buildFromSendIndividualMessage($request, $client, null);

        // Assert - This is the most critical assertion
        $this->assertNull($message->campaign_id, 'Real-time messages must have campaign_id = null');
    }

    public function test_factory_can_be_instantiated_via_app_make()
    {
        // This test ensures the factory can be resolved by Laravel's container
        // which is important for dependency injection

        // Act
        $factory = app()->make(MessageFactory::class);

        // Assert
        $this->assertInstanceOf(MessageFactory::class, $factory);
    }
}
