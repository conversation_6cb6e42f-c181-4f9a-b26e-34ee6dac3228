<?php

namespace Tests\Unit\UseCases\WhatsApp;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\UseCases\WhatsApp\SendRealTimeMessage;
use App\UseCases\Inventory\Client\Get as GetClient;
use App\UseCases\ChatBot\PhoneNumber\Get as GetPhoneNumber;
use App\UseCases\ChatBot\Template\Get as GetTemplate;
use App\Repositories\MessageRepository;
use App\Factories\ChatBot\MessageFactory;
use App\Services\Meta\WhatsApp\MessageService;
use App\Http\Requests\WhatsApp\SendRealTimeMessageRequest;
use App\Domains\ChatBot\Message;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\ChatBot\Template;
use App\Domains\Inventory\Client;
use App\Models\User;
use App\Models\Organization;
use App\Enums\MessageStatus;
use Illuminate\Support\Facades\DB;

class SendRealTimeMessageTest extends TestCase
{
    use RefreshDatabase;

    private SendRealTimeMessage $useCase;
    private MessageRepository $messageRepository;
    private MessageFactory $messageFactory;
    private GetClient $getClient;
    private GetPhoneNumber $getPhoneNumber;
    private GetTemplate $getTemplate;
    private MessageService $messageService;
    private Organization $organization;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        $this->actingAs($this->user);

        // Mock dependencies
        $this->messageRepository = $this->createMock(MessageRepository::class);
        $this->messageFactory = $this->createMock(MessageFactory::class);
        $this->getClient = $this->createMock(GetClient::class);
        $this->getPhoneNumber = $this->createMock(GetPhoneNumber::class);
        $this->getTemplate = $this->createMock(GetTemplate::class);

        $this->useCase = new SendRealTimeMessage(
            $this->messageRepository,
            $this->messageFactory,
            $this->getClient,
            $this->getPhoneNumber,
            $this->getTemplate
        );
    }

    private function createTestClient(int $id = 1, ?int $organizationId = null): Client
    {
        return new Client(
            id: $id,
            organization_id: $organizationId ?? $this->organization->id,
            name: 'John Doe',
            phone: '+5511999999999',
            whatsapp_from: null,
            email: null,
            profession: null,
            birthdate: null,
            cpf: null,
            cnpj: null,
            service: null,
            address: null,
            number: null,
            neighborhood: null,
            cep: null,
            complement: null,
            civil_state: null,
            description: null
        );
    }

    private function createTestPhoneNumber(int $id = 1, ?int $organizationId = null): PhoneNumber
    {
        return new PhoneNumber(
            id: $id,
            organization_id: $organizationId ?? $this->organization->id,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+5511888888888',
            name: 'Test Phone',
            description: null,
            is_active: true,
            whatsapp_phone_number_id: null,
            whatsapp_business_id: null,
            whatsapp_access_token: null
        );
    }

    private function createTestTemplate(int $id = 5, ?int $organizationId = null): Template
    {
        return new Template(
            id: $id,
            organization_id: $organizationId ?? $this->organization->id,
            phone_number_id: null,
            user_id: null,
            client_id: null,
            name: 'Test Template',
            category: null,
            parameter_format: null,
            language: null,
            library_template_name: null,
            id_external: null,
            status: null
        );
    }

    private function createTestMessage(
        ?int $id = null,
        ?int $campaignId = null,
        ?int $templateId = null,
        ?int $clientId = 1,
        ?string $messageText = 'Test message',
        ?Client $client = null,
        ?Template $template = null
    ): Message {
        return new Message(
            id: $id,
            organization_id: $this->organization->id,
            campaign_id: $campaignId,
            template_id: $templateId,
            client_id: $clientId,
            message: $messageText,
            status: MessageStatus::is_sending,
            is_sent: false,
            is_delivered: false,
            is_fail: false,
            is_read: false,
            is_direct_message: true,
            delivery_attempts: 0,
            last_attempt_at: null,
            max_retries: 3,
            next_retry_at: null,
            last_error_message: null,
            sent_at: null,
            scheduled_at: null,
            created_at: null,
            updated_at: null,
            client: $client,
            template: $template,
            campaign: null,
            delivery_attempts_history: null
        );
    }

    public function test_can_send_realtime_message_successfully()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello {{client.name}}!',
            'client_id' => 1,
            'phone_number_id' => 1,
            'template_id' => null,
            'is_direct_message' => true
        ]);

        $client = $this->createTestClient();

        $phoneNumber = $this->createTestPhoneNumber();

        $message = $this->createTestMessage(
            id: null,
            campaignId: null,
            templateId: null,
            clientId: 1,
            messageText: 'Hello John Doe!',
            client: $client,
            template: null
        );

        $savedMessage = $this->createTestMessage(
            id: 123,
            campaignId: null,
            templateId: null,
            clientId: 1,
            messageText: 'Hello John Doe!',
            client: $client,
            template: null
        );

        $whatsappResponse = [
            'messages' => [
                ['id' => 'wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI']
            ],
            'messaging_product' => 'whatsapp',
            'contacts' => [
                ['input' => '+5511999999999', 'wa_id' => '5511999999999']
            ]
        ];

        // Mock expectations
        $this->getClient->expects($this->exactly(2))
                       ->method('perform')
                       ->with(1)
                       ->willReturn($client);

        $this->getPhoneNumber->expects($this->exactly(2))
                            ->method('perform')
                            ->with(1)
                            ->willReturn($phoneNumber);

        $this->messageFactory->expects($this->once())
                            ->method('buildFromSendIndividualMessage')
                            ->with($request, $client, null)
                            ->willReturn($message);

        $this->messageRepository->expects($this->once())
                               ->method('store')
                               ->with($message)
                               ->willReturn($savedMessage);

        // Mock MessageService
        $this->app->bind(MessageService::class, function () use ($whatsappResponse) {
            $mock = $this->createMock(MessageService::class);
            $mock->expects($this->once())
                 ->method('send')
                 ->willReturn($whatsappResponse);
            return $mock;
        });

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals(123, $result['message_id']);
        $this->assertEquals('wamid.HBgLNTU3OTkxNTE0OTU3FQIAEhggRDdBQjlCNzNCNzA4QzlGNEE4QjY4RjVGNzU4NzI1RTI', $result['whatsapp_message_id']);
        $this->assertEquals('sent', $result['status']);
        $this->assertEquals($whatsappResponse, $result['meta_response']);
    }

    public function test_can_send_realtime_message_with_template()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Template message',
            'client_id' => 1,
            'phone_number_id' => 1,
            'template_id' => 5,
            'is_direct_message' => true
        ]);

        $client = $this->createTestClient();

        $phoneNumber = $this->createTestPhoneNumber();

        $template = $this->createTestTemplate();

        $message = $this->createTestMessage(
            id: null,
            campaignId: null,
            templateId: 5,
            clientId: 1,
            messageText: 'Template message',
            client: $client,
            template: $template
        );

        $savedMessage = $this->createTestMessage(
            id: 124,
            campaignId: null,
            templateId: 5,
            clientId: 1,
            messageText: 'Template message',
            client: $client,
            template: $template
        );

        // Mock expectations
        $this->getClient->expects($this->exactly(2))
                       ->method('perform')
                       ->with(1)
                       ->willReturn($client);

        $this->getPhoneNumber->expects($this->exactly(2))
                            ->method('perform')
                            ->with(1)
                            ->willReturn($phoneNumber);

        $this->getTemplate->expects($this->exactly(2))
                         ->method('perform')
                         ->with(5)
                         ->willReturn($template);

        $this->messageFactory->expects($this->once())
                            ->method('buildFromSendIndividualMessage')
                            ->with($request, $client, $template)
                            ->willReturn($message);

        $this->messageRepository->expects($this->once())
                               ->method('store')
                               ->with($message)
                               ->willReturn($savedMessage);

        // Mock MessageService
        $this->app->bind(MessageService::class, function () {
            $mock = $this->createMock(MessageService::class);
            $mock->expects($this->once())
                 ->method('send')
                 ->willReturn(['messages' => [['id' => 'wamid.template123']]]);
            return $mock;
        });

        // Act
        $result = $this->useCase->perform($request);

        // Assert
        $this->assertEquals(124, $result['message_id']);
        $this->assertEquals('wamid.template123', $result['whatsapp_message_id']);
    }

    public function test_validates_client_belongs_to_organization()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello!',
            'client_id' => 1,
            'phone_number_id' => 1
        ]);

        $clientFromDifferentOrg = $this->createTestClient(1, 999);

        $this->getClient->expects($this->once())
                       ->method('perform')
                       ->with(1)
                       ->willReturn($clientFromDifferentOrg);

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Client does not belong to your organization');

        $this->useCase->perform($request);
    }

    public function test_validates_phone_number_belongs_to_organization()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello!',
            'client_id' => 1,
            'phone_number_id' => 1
        ]);

        $client = $this->createTestClient();

        $phoneNumberFromDifferentOrg = $this->createTestPhoneNumber(1, 999);

        $this->getClient->expects($this->once())
                       ->method('perform')
                       ->with(1)
                       ->willReturn($client);

        $this->getPhoneNumber->expects($this->once())
                            ->method('perform')
                            ->with(1)
                            ->willReturn($phoneNumberFromDifferentOrg);

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Phone number does not belong to your organization');

        $this->useCase->perform($request);
    }

    public function test_validates_template_belongs_to_organization()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello!',
            'client_id' => 1,
            'phone_number_id' => 1,
            'template_id' => 5
        ]);

        $client = $this->createTestClient();

        $phoneNumber = $this->createTestPhoneNumber();

        $templateFromDifferentOrg = $this->createTestTemplate(5, 999);

        $this->getClient->expects($this->once())
                       ->method('perform')
                       ->willReturn($client);

        $this->getPhoneNumber->expects($this->once())
                            ->method('perform')
                            ->willReturn($phoneNumber);

        $this->getTemplate->expects($this->once())
                         ->method('perform')
                         ->with(5)
                         ->willReturn($templateFromDifferentOrg);

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Template does not belong to your organization');

        $this->useCase->perform($request);
    }

    public function test_handles_whatsapp_service_failure()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello!',
            'client_id' => 1,
            'phone_number_id' => 1
        ]);

        $client = $this->createTestClient();

        $phoneNumber = $this->createTestPhoneNumber();

        $message = $this->createTestMessage(
            id: null,
            campaignId: null,
            templateId: null,
            clientId: 1,
            messageText: 'Hello!',
            client: $client
        );

        $savedMessage = $this->createTestMessage(
            id: 125,
            campaignId: null,
            templateId: null,
            clientId: 1,
            messageText: 'Hello!',
            client: $client
        );

        // Mock expectations
        $this->getClient->expects($this->exactly(2))
                       ->method('perform')
                       ->willReturn($client);

        $this->getPhoneNumber->expects($this->exactly(2))
                            ->method('perform')
                            ->willReturn($phoneNumber);

        $this->messageFactory->expects($this->once())
                            ->method('buildFromSendIndividualMessage')
                            ->willReturn($message);

        $this->messageRepository->expects($this->once())
                               ->method('store')
                               ->willReturn($savedMessage);

        // Mock MessageService to throw exception
        $this->app->bind(MessageService::class, function () {
            $mock = $this->createMock(MessageService::class);
            $mock->expects($this->once())
                 ->method('send')
                 ->willThrowException(new \Exception('WhatsApp API Error'));
            return $mock;
        });

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('WhatsApp API Error');

        $this->useCase->perform($request);
    }

    public function test_rolls_back_transaction_on_failure()
    {
        // Arrange
        $request = new SendRealTimeMessageRequest([
            'text' => 'Hello!',
            'client_id' => 1,
            'phone_number_id' => 1
        ]);

        $client = $this->createTestClient();

        $phoneNumber = $this->createTestPhoneNumber();

        // Mock expectations
        $this->getClient->expects($this->exactly(2))
                       ->method('perform')
                       ->willReturn($client);

        $this->getPhoneNumber->expects($this->exactly(2))
                            ->method('perform')
                            ->willReturn($phoneNumber);

        $this->messageRepository->expects($this->once())
                               ->method('store')
                               ->willThrowException(new \Exception('Database error'));

        // Spy on DB transactions
        DB::spy();

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database error');

        $this->useCase->perform($request);

        // Verify transaction was rolled back
        DB::shouldHaveReceived('rollBack')->once();
    }
}
