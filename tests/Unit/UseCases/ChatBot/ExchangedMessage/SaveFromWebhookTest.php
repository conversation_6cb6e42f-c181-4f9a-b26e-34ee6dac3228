<?php

namespace Tests\Unit\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\ExchangedMessage;
use App\Domains\ChatBot\PhoneNumber;
use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Repositories\ClientRepository;
use App\Repositories\ExchangedMessageRepository;
use App\UseCases\ChatBot\ExchangedMessage\SaveFromWebhook;
use Carbon\Carbon;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class SaveFromWebhookTest extends TestCase
{
    use RefreshDatabase;

    private SaveFromWebhook $useCase;
    private ExchangedMessageFactory $exchangedMessageFactory;
    private ExchangedMessageRepository $exchangedMessageRepository;
    private ClientRepository $clientRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->exchangedMessageFactory = app()->make(ExchangedMessageFactory::class);
        $this->exchangedMessageRepository = app()->make(ExchangedMessageRepository::class);
        $this->clientRepository = app()->make(ClientRepository::class);

        $this->useCase = new SaveFromWebhook(
            $this->exchangedMessageFactory,
            $this->exchangedMessageRepository,
            $this->clientRepository
        );
    }

    public function test_should_not_process_webhook_without_message_data()
    {
        $organization = $this->createOrganization();
        $phoneNumber = $this->createPhoneNumber($organization);

        $webhookData = [
            'metadata' => ['phone_number_id' => '123'],
            'statuses' => [['id' => 'status1']]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('Webhook does not contain processable message data', $result['reason']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_should_not_process_webhook_without_sender_phone()
    {
        $organization = $this->createOrganization();
        $phoneNumber = $this->createPhoneNumber($organization);

        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello']
                // Missing 'from' field
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertFalse($result['success']);
        $this->assertEquals('Webhook does not contain processable message data', $result['reason']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_should_process_valid_text_message_with_existing_client()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);
        $clientModel = \App\Models\Client::factory()->create([
            'organization_id' => $organizationModel->id,
            'phone' => '557981166640'
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);
        $client = app()->make(\App\Factories\Inventory\ClientFactory::class)->buildFromModel($clientModel);

        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640',
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello World']
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('Message processed and saved successfully', $result['reason']);
        $this->assertEquals(1, $result['processed']);
        $this->assertArrayHasKey('exchanged_message_id', $result);
        $this->assertEquals($client->id, $result['client_id']);
    }

    public function test_should_create_new_client_if_not_found()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);

        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640',
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello World'],
                'contact_name' => 'John Doe'
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals('Message processed and saved successfully', $result['reason']);
        $this->assertEquals(1, $result['processed']);
        $this->assertArrayHasKey('exchanged_message_id', $result);
        $this->assertArrayHasKey('client_id', $result);
    }

    public function test_should_extract_text_from_different_message_types()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);

        // Test button message
        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640',
                'timestamp' => '1757554504',
                'type' => 'button',
                'button' => ['text' => 'Button Text', 'payload' => 'button_payload']
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);

        // Verify the message was saved with correct text
        $exchangedMessage = $this->exchangedMessageRepository->fetchById($result['exchanged_message_id'], $organization->id);
        $this->assertEquals('Button Text', $exchangedMessage->message);
    }

    public function test_should_find_client_with_phone_variations()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);
        $clientModel = \App\Models\Client::factory()->create([
            'organization_id' => $organizationModel->id,
            'phone' => '7981166640' // Phone without country code and DDD formatting
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);
        $client = app()->make(\App\Factories\Inventory\ClientFactory::class)->buildFromModel($clientModel);

        // Webhook comes with unformatted phone
        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640',
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello World']
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals($client->id, $result['client_id']);
    }

    public function test_should_find_client_by_whatsapp_from()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);
        $clientModel = \App\Models\Client::factory()->create([
            'organization_id' => $organizationModel->id,
            'phone' => '11999999999',
            'whatsapp_from' => '557981166640' // WhatsApp from field set
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);
        $client = app()->make(\App\Factories\Inventory\ClientFactory::class)->buildFromModel($clientModel);

        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640', // Exact match with whatsapp_from
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello World']
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals($client->id, $result['client_id']);
    }

    public function test_should_update_whatsapp_from_when_found_by_phone_variation()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);
        $clientModel = \App\Models\Client::factory()->create([
            'organization_id' => $organizationModel->id,
            'phone' => '7981166640', // Phone without country code
            'whatsapp_from' => null // No whatsapp_from initially
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);

        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640', // Full format that should match via variations
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello World']
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertEquals($clientModel->id, $result['client_id']);

        // Verify that whatsapp_from was updated in the database
        $updatedClient = \App\Models\Client::find($clientModel->id);
        $this->assertEquals('557981166640', $updatedClient->whatsapp_from);
    }

    public function test_should_create_new_client_with_whatsapp_from()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);

        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640', // New phone number
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello World']
            ]
        ];

        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);

        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('client_id', $result);

        // Verify that the new client was created with whatsapp_from
        $createdClient = \App\Models\Client::find($result['client_id']);
        $this->assertEquals('557981166640', $createdClient->whatsapp_from);
        $this->assertEquals('557981166640', $createdClient->phone);
    }

    public function test_whatsapp_from_search_is_faster_than_phone_variations()
    {
        // Create real database records
        $organizationModel = \App\Models\Organization::factory()->create();
        $phoneNumberModel = \App\Models\PhoneNumber::factory()->create([
            'organization_id' => $organizationModel->id
        ]);

        // Create multiple clients to simulate a larger database
        for ($i = 0; $i < 50; $i++) {
            \App\Models\Client::factory()->create([
                'organization_id' => $organizationModel->id,
                'phone' => '11999' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'whatsapp_from' => null
            ]);
        }

        // Create the target client with whatsapp_from
        $targetClient = \App\Models\Client::factory()->create([
            'organization_id' => $organizationModel->id,
            'phone' => '11999999999',
            'whatsapp_from' => '557981166640'
        ]);

        // Create domain objects from models
        $organization = app()->make(\App\Factories\OrganizationFactory::class)->buildFromModel($organizationModel);
        $phoneNumber = app()->make(\App\Factories\ChatBot\PhoneNumberFactory::class)->buildFromModel($phoneNumberModel);

        $webhookData = [
            'message' => [
                'id' => 'msg123',
                'from' => '557981166640', // Should find via whatsapp_from immediately
                'timestamp' => '1757554504',
                'type' => 'text',
                'text' => ['body' => 'Hello World']
            ]
        ];

        $startTime = microtime(true);
        $result = $this->useCase->perform($webhookData, $organization, $phoneNumber);
        $endTime = microtime(true);

        $this->assertTrue($result['success']);
        $this->assertEquals($targetClient->id, $result['client_id']);

        // The search should be fast (less than 100ms even with 50+ clients)
        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $this->assertLessThan(100, $executionTime, 'WhatsApp from search should be fast');
    }

    private function createOrganization(): Organization
    {
        return new Organization(
            id: 1,
            name: 'Test Organization',
            description: 'Test Description',
            is_active: true,
            is_suspended: false,
            default_flow_id: null,
            whatsapp_webhook_verify_token: null,
            whatsapp_webhook_secret: null,
            email: '<EMAIL>',
            cpf_cnpj: '*********01234',
            company_type: null,
            phone: '*********',
            mobile_phone: null,
            address: 'Test Address',
            address_number: '123',
            complement: null,
            province: 'Test Neighborhood',
            city: 'Test City',
            state: 'TS',
            postal_code: '********',
            birth_date: null,
            asaas_account_id: null,
            asaas_api_key: null,
            asaas_wallet_id: null,
            asaas_environment: null,
            asaas_subscription_id: null,
            subscription_status: null,
            subscription_value: null,
            subscription_due_date: null,
            subscription_started_at: null,
            subscription_expires_at: null,
            is_courtesy: null,
            courtesy_expires_at: null,
            courtesy_reason: null,
            monthly_revenue: null,
            income_value: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now()
        );
    }

    private function createPhoneNumber(Organization $organization): PhoneNumber
    {
        return new PhoneNumber(
            id: 1,
            organization_id: $organization->id,
            user_id: null,
            client_id: null,
            flow_id: null,
            phone_number: '+*************',
            name: 'Test Phone',
            description: 'Test Phone Description',
            is_active: true,
            whatsapp_phone_number_id: '661703160367895',
            whatsapp_business_id: '1759027478334690',
            whatsapp_access_token: null,
            created_at: Carbon::now(),
            updated_at: Carbon::now(),
            deleted_at: null,
            organization: $organization,
            flow: null
        );
    }

    private function createClient(Organization $organization, string $phone): Client
    {
        $client = new Client(
            null,
            $organization->id,
            'Test Client',
            $phone,
            '<EMAIL>',
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            Carbon::now(),
            Carbon::now()
        );

        return $this->clientRepository->store($client);
    }
}
