<?php

namespace Tests\Unit\UseCases\ChatBot\ExchangedMessage;

use App\Domains\ChatBot\ExchangedMessage;
use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Repositories\ClientRepository;
use App\Repositories\ExchangedMessageRepository;
use App\UseCases\ChatBot\ExchangedMessage\GetChatByClient;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class GetChatByClientTest extends TestCase
{
    use RefreshDatabase;

    private GetChatByClient $useCase;
    private ExchangedMessageRepository $exchangedMessageRepository;
    private ClientRepository $clientRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->exchangedMessageRepository = $this->createMock(ExchangedMessageRepository::class);
        $this->clientRepository = $this->createMock(ClientRepository::class);

        $this->useCase = new GetChatByClient(
            $this->exchangedMessageRepository,
            $this->clientRepository
        );
    }

    public function test_should_not_process_when_client_not_found()
    {
        $organization = $this->createMockOrganization();

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(999)
            ->willThrowException(new \Exception('Client not found'));

        $result = $this->useCase->perform(999, $organization);

        $this->assertFalse($result['success']);
        $this->assertEquals('Client not found', $result['reason']);
        $this->assertEquals([], $result['data']);
        $this->assertEquals(0, $result['count']);
    }

    public function test_should_not_process_when_client_does_not_belong_to_organization()
    {
        $organization = $this->createMockOrganization(1);
        $client = $this->createMockClient(789, 2); // Different organization

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willReturn($client);

        $result = $this->useCase->perform(789, $organization);

        $this->assertFalse($result['success']);
        $this->assertEquals('Client does not belong to organization', $result['reason']);
        $this->assertEquals([], $result['data']);
        $this->assertEquals(0, $result['count']);
    }

    public function test_should_return_empty_chat_when_no_messages()
    {
        $organization = $this->createMockOrganization(1);
        $client = $this->createMockClient(789, 1);

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willReturn($client);

        $this->exchangedMessageRepository
            ->expects($this->once())
            ->method('fetchChatByClient')
            ->with(789, 1, 50)
            ->willReturn([]);

        $result = $this->useCase->perform(789, $organization);

        $this->assertTrue($result['success']);
        $this->assertEquals('Chat history retrieved successfully', $result['reason']);
        $this->assertEquals([], $result['data']);
        $this->assertEquals(0, $result['count']);
        $this->assertEquals(789, $result['client_id']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals(50, $result['limit']);
    }

    public function test_should_return_chat_history_successfully()
    {
        $organization = $this->createMockOrganization(1);
        $client = $this->createMockClient(789, 1);
        
        $exchangedMessage1 = $this->createMockExchangedMessage(1, true, false); // Inbound
        $exchangedMessage2 = $this->createMockExchangedMessage(2, false, true); // Outbound
        $exchangedMessages = [$exchangedMessage1, $exchangedMessage2];

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willReturn($client);

        $this->exchangedMessageRepository
            ->expects($this->once())
            ->method('fetchChatByClient')
            ->with(789, 1, 50)
            ->willReturn($exchangedMessages);

        $result = $this->useCase->perform(789, $organization);

        $this->assertTrue($result['success']);
        $this->assertEquals('Chat history retrieved successfully', $result['reason']);
        $this->assertCount(2, $result['data']);
        $this->assertEquals(2, $result['count']);
        $this->assertEquals(789, $result['client_id']);
        $this->assertEquals(1, $result['organization_id']);
        $this->assertEquals(50, $result['limit']);
    }

    public function test_should_respect_custom_limit()
    {
        $organization = $this->createMockOrganization(1);
        $client = $this->createMockClient(789, 1);

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willReturn($client);

        $this->exchangedMessageRepository
            ->expects($this->once())
            ->method('fetchChatByClient')
            ->with(789, 1, 25)
            ->willReturn([]);

        $result = $this->useCase->perform(789, $organization, 25);

        $this->assertTrue($result['success']);
        $this->assertEquals(25, $result['limit']);
    }

    private function createMockOrganization(int $id = 1): Organization
    {
        return new Organization(
            $id, 'Test Org', null, null, null, null, null, null, null, null, null,
            true, false, null, null, null, null, null, null, null
        );
    }

    private function createMockClient(int $id, int $organizationId): Client
    {
        return new Client(
            $id, $organizationId, 'Test Client', '+5511999999999', '557981166640', '<EMAIL>',
            null, null, null, null, null, null, null, null, null, null, null, null,
            Carbon::now(), Carbon::now(), null, null
        );
    }

    private function createMockExchangedMessage(int $id, bool $inbound, bool $outbound): ExchangedMessage
    {
        $exchangedMessage = $this->createMock(ExchangedMessage::class);
        $exchangedMessage->method('toArray')->willReturn([
            'id' => $id,
            'organization_id' => 1,
            'client_id' => 789,
            'phone_number_id' => 1,
            'inbound' => $inbound,
            'outbound' => $outbound,
            'message' => 'Test message ' . $id,
            'sent_at' => Carbon::now()->format('Y-m-d H:i:s')
        ]);
        
        return $exchangedMessage;
    }
}
