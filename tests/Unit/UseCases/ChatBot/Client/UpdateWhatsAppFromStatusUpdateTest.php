<?php

namespace Tests\Unit\UseCases\ChatBot\Client;

use App\Domains\ChatBot\Message;
use App\Domains\Inventory\Client;
use App\Domains\Organization;
use App\Repositories\ClientRepository;
use App\UseCases\ChatBot\Client\UpdateWhatsAppFromStatusUpdate;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UpdateWhatsAppFromStatusUpdateTest extends TestCase
{
    use RefreshDatabase;

    private UpdateWhatsAppFromStatusUpdate $useCase;
    private ClientRepository $clientRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->clientRepository = $this->createMock(ClientRepository::class);
        $this->useCase = new UpdateWhatsAppFromStatusUpdate($this->clientRepository);
    }

    public function test_should_not_process_message_without_client_id()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'delivered',
            'timestamp' => '1234567890',
            'recipient_id' => '557981166640'
        ];

        $organization = $this->createMockOrganization();
        $message = $this->createMockMessage(null); // No client_id

        $result = $this->useCase->perform($statusData, $organization, $message);

        $this->assertFalse($result['success']);
        $this->assertEquals('Message has no client_id', $result['reason']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_should_not_process_when_client_not_found()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'delivered',
            'timestamp' => '1234567890',
            'recipient_id' => '557981166640'
        ];

        $organization = $this->createMockOrganization();
        $message = $this->createMockMessage(789);

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willThrowException(new \Exception('Client not found'));

        $result = $this->useCase->perform($statusData, $organization, $message);

        $this->assertFalse($result['success']);
        $this->assertEquals('Client not found', $result['reason']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_should_not_process_when_client_already_has_whatsapp_from()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'delivered',
            'timestamp' => '1234567890',
            'recipient_id' => '557981166640'
        ];

        $organization = $this->createMockOrganization();
        $message = $this->createMockMessage(789);
        $client = $this->createMockClient(789, '557981166640'); // Already has whatsapp_from

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willReturn($client);

        $result = $this->useCase->perform($statusData, $organization, $message);

        $this->assertTrue($result['success']);
        $this->assertEquals('Client already has whatsapp_from', $result['reason']);
        $this->assertEquals(0, $result['processed']);
        $this->assertEquals('557981166640', $result['whatsapp_from']);
    }

    public function test_should_not_process_when_no_recipient_id()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'delivered',
            'timestamp' => '1234567890'
            // No recipient_id
        ];

        $organization = $this->createMockOrganization();
        $message = $this->createMockMessage(789);
        $client = $this->createMockClient(789, null); // No whatsapp_from

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willReturn($client);

        $result = $this->useCase->perform($statusData, $organization, $message);

        $this->assertFalse($result['success']);
        $this->assertEquals('No recipient_id found in status data', $result['reason']);
        $this->assertEquals(0, $result['processed']);
    }

    public function test_should_update_client_whatsapp_from_successfully()
    {
        $statusData = [
            'id' => 'wamid.123',
            'status' => 'delivered',
            'timestamp' => '1234567890',
            'recipient_id' => '557981166640'
        ];

        $organization = $this->createMockOrganization();
        $message = $this->createMockMessage(789);
        $client = $this->createMockClient(789, null); // No whatsapp_from
        $updatedClient = $this->createMockClient(789, '557981166640'); // With whatsapp_from

        $this->clientRepository
            ->expects($this->once())
            ->method('fetchById')
            ->with(789)
            ->willReturn($client);

        $this->clientRepository
            ->expects($this->once())
            ->method('update')
            ->with($client, $organization->id)
            ->willReturn($updatedClient);

        $result = $this->useCase->perform($statusData, $organization, $message);

        $this->assertTrue($result['success']);
        $this->assertEquals('Client whatsapp_from updated successfully', $result['reason']);
        $this->assertEquals(1, $result['processed']);
        $this->assertEquals(789, $result['client_id']);
        $this->assertEquals('557981166640', $result['whatsapp_from']);
        $this->assertNull($result['previous_whatsapp_from']);
    }

    private function createMockOrganization(): Organization
    {
        return new Organization(
            1, 'Test Org', null, null, null, null, null, null, null, null, null,
            true, false, null, null, null, null, null, null, null
        );
    }

    private function createMockMessage(?int $clientId): Message
    {
        return new Message(
            456, 1, null, null, $clientId, 'Test message', null, false, false, false, false, false,
            0, null, 3, null, null, null, null, null, null, null, null, null
        );
    }

    private function createMockClient(int $id, ?string $whatsappFrom): Client
    {
        return new Client(
            $id, 1, 'Test Client', '+5511999999999', $whatsappFrom, '<EMAIL>',
            null, null, null, null, null, null, null, null, null, null, null, null,
            Carbon::now(), Carbon::now(), null, null
        );
    }
}
