<?php

namespace Tests\Feature\UseCases\ChatBot\ExchangedMessage;

use App\Factories\ChatBot\ExchangedMessageFactory;
use App\Factories\ChatBot\MessageFactory;
use App\Factories\ChatBot\PhoneNumberFactory;
use App\Factories\OrganizationFactory;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\Message;
use App\Repositories\ExchangedMessageRepository;
use App\UseCases\ChatBot\ExchangedMessage\SaveOutboundFromStatusUpdate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaveOutboundFromStatusUpdateRaceConditionTest extends TestCase
{
    use RefreshDatabase;

    private SaveOutboundFromStatusUpdate $useCase;
    private ExchangedMessageFactory $exchangedMessageFactory;
    private ExchangedMessageRepository $exchangedMessageRepository;
    private OrganizationFactory $organizationFactory;
    private PhoneNumberFactory $phoneNumberFactory;
    private MessageFactory $messageFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->exchangedMessageFactory = app()->make(ExchangedMessageFactory::class);
        $this->exchangedMessageRepository = app()->make(ExchangedMessageRepository::class);
        $this->organizationFactory = app()->make(OrganizationFactory::class);
        $this->phoneNumberFactory = app()->make(PhoneNumberFactory::class);
        $this->messageFactory = app()->make(MessageFactory::class);

        $this->useCase = new SaveOutboundFromStatusUpdate(
            $this->exchangedMessageFactory,
            $this->exchangedMessageRepository
        );
    }

    public function test_should_handle_concurrent_webhook_processing_gracefully()
    {
        // Arrange
        $organizationModel = Organization::factory()->create();
        $phoneNumberModel = PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $clientModel = \App\Models\Client::factory()->create(['organization_id' => $organizationModel->id]);
        $messageModel = Message::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $phoneNumber = $this->phoneNumberFactory->buildFromModel($phoneNumberModel);
        $message = $this->messageFactory->buildFromModel($messageModel, true);

        $statusData = [
            'status' => 'delivered',
            'timestamp' => time(),
            'id' => 'wamid.test123'
        ];

        // Act - Simulate concurrent processing by running the same webhook multiple times
        $results = [];
        $successCount = 0;
        $duplicateCount = 0;

        // Run 5 concurrent webhook processes
        for ($i = 0; $i < 5; $i++) {
            $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);
            $results[] = $result;

            if ($result['success']) {
                $successCount++;
            } elseif (!$result['success'] && str_contains($result['reason'] ?? '', 'already exists')) {
                $duplicateCount++;
            }
        }

        // Assert - Only one should succeed, others should be prevented by unique constraint
        $this->assertEquals(1, $successCount, 'Exactly one webhook should succeed');
        $this->assertEquals(4, $duplicateCount, 'Four webhooks should be prevented as duplicates');

        // Verify only one ExchangedMessage exists in database
        $exchangedMessagesCount = \App\Models\ExchangedMessage::where('message_id', $message->id)->count();
        $this->assertEquals(1, $exchangedMessagesCount, 'Only one ExchangedMessage should exist in database');

        // Verify the ExchangedMessage has correct data
        $exchangedMessage = \App\Models\ExchangedMessage::where('message_id', $message->id)->first();
        $this->assertNotNull($exchangedMessage);
        $this->assertEquals($message->id, $exchangedMessage->message_id);
        $this->assertEquals($organization->id, $exchangedMessage->organization_id);
        $this->assertTrue($exchangedMessage->outbound);
        $this->assertFalse($exchangedMessage->inbound);
    }

    public function test_should_handle_database_unique_constraint_violation()
    {
        // Arrange
        $organizationModel = Organization::factory()->create();
        $phoneNumberModel = PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $clientModel = \App\Models\Client::factory()->create(['organization_id' => $organizationModel->id]);
        $messageModel = Message::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $phoneNumber = $this->phoneNumberFactory->buildFromModel($phoneNumberModel);
        $message = $this->messageFactory->buildFromModel($messageModel, true);

        $statusData = [
            'status' => 'delivered',
            'timestamp' => time(),
            'id' => 'wamid.test123'
        ];

        // Pre-create an ExchangedMessage to simulate race condition
        $existingExchangedMessage = $this->exchangedMessageFactory->buildFromOutboundMessage(
            $message,
            $phoneNumber,
            now()
        );
        $this->exchangedMessageRepository->store($existingExchangedMessage);

        // Act - Try to create another ExchangedMessage for the same message
        $result = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Assert - Should detect existing and return appropriate response
        $this->assertFalse($result['success']);
        $this->assertEquals('ExchangedMessage already exists for this message', $result['reason']);
        $this->assertEquals(0, $result['processed']);

        // Verify still only one ExchangedMessage exists
        $exchangedMessagesCount = \App\Models\ExchangedMessage::where('message_id', $message->id)->count();
        $this->assertEquals(1, $exchangedMessagesCount);
    }

    public function test_should_handle_unique_constraint_exception_gracefully()
    {
        // Arrange
        $organizationModel = Organization::factory()->create();
        $phoneNumberModel = PhoneNumber::factory()->create(['organization_id' => $organizationModel->id]);
        $clientModel = \App\Models\Client::factory()->create(['organization_id' => $organizationModel->id]);
        $messageModel = Message::factory()->create([
            'organization_id' => $organizationModel->id,
            'client_id' => $clientModel->id,
            'phone_number_id' => $phoneNumberModel->id
        ]);

        $organization = $this->organizationFactory->buildFromModel($organizationModel);
        $phoneNumber = $this->phoneNumberFactory->buildFromModel($phoneNumberModel);
        $message = $this->messageFactory->buildFromModel($messageModel, true);

        $statusData = [
            'status' => 'delivered',
            'timestamp' => time(),
            'id' => 'wamid.test123'
        ];

        // Act - First call should succeed
        $result1 = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Act - Second call should trigger unique constraint and be handled gracefully
        $result2 = $this->useCase->perform($statusData, $organization, $phoneNumber, $message);

        // Assert - First call succeeded
        $this->assertTrue($result1['success']);
        $this->assertEquals(1, $result1['processed']);

        // Assert - Second call was handled gracefully
        $this->assertFalse($result2['success']);
        $this->assertEquals('ExchangedMessage already exists for this message', $result2['reason']);
        $this->assertEquals(0, $result2['processed']);

        // Verify only one ExchangedMessage exists
        $exchangedMessagesCount = \App\Models\ExchangedMessage::where('message_id', $message->id)->count();
        $this->assertEquals(1, $exchangedMessagesCount);
    }
}
