<?php

namespace Tests\Feature\Controllers\ChatBot;

use Tests\TestCase;
use App\Models\User;
use App\Models\Organization;
use App\Models\Client;
use App\Models\ExchangedMessage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;

class ExchangedMessageControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create([
            'organization_id' => $this->organization->id
        ]);
        $this->client = Client::factory()->create([
            'organization_id' => $this->organization->id
        ]);

        Sanctum::actingAs($this->user);
    }

    public function test_can_list_exchanged_messages()
    {
        // Create some exchanged messages
        ExchangedMessage::factory()->count(3)->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
        ]);

        $response = $this->getJson('/api/exchanged_messages');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'status',
                    'data' => [
                        '*' => [
                            'id',
                            'organization_id',
                            'client_id',
                            'inbound',
                            'outbound',
                            'message',
                            'json',
                            'sent_at',
                            'created_at',
                            'updated_at'
                        ]
                    ],
                    'errors',
                    'pagination'
                ]);
    }

    public function test_can_create_exchanged_message()
    {
        $data = [
            'client_id' => $this->client->id,
            'inbound' => true,
            'outbound' => false,
            'message' => 'Test message from webhook',
            'json' => ['webhook' => 'data'],
            'sent_at' => now()->format('Y-m-d H:i:s'),
        ];

        $response = $this->postJson('/api/exchanged_messages', $data);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'status',
                    'data' => [
                        'id',
                        'organization_id',
                        'client_id',
                        'inbound',
                        'outbound',
                        'message',
                        'json',
                        'sent_at',
                        'created_at',
                        'updated_at'
                    ],
                    'errors'
                ]);

        $this->assertDatabaseHas('exchanged_messages', [
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'inbound' => true,
            'outbound' => false,
            'message' => 'Test message from webhook',
        ]);
    }

    public function test_can_show_exchanged_message()
    {
        $exchangedMessage = ExchangedMessage::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
        ]);

        $response = $this->getJson("/api/exchanged_messages/{$exchangedMessage->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'message',
                    'status',
                    'data' => [
                        'id',
                        'organization_id',
                        'client_id',
                        'inbound',
                        'outbound',
                        'message',
                        'json',
                        'sent_at',
                        'created_at',
                        'updated_at'
                    ],
                    'errors'
                ]);
    }

    public function test_can_update_exchanged_message()
    {
        $exchangedMessage = ExchangedMessage::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'message' => 'Original message',
        ]);

        $data = [
            'message' => 'Updated message',
            'inbound' => false,
            'outbound' => true,
        ];

        $response = $this->putJson("/api/exchanged_messages/{$exchangedMessage->id}", $data);

        $response->assertStatus(200);

        $this->assertDatabaseHas('exchanged_messages', [
            'id' => $exchangedMessage->id,
            'message' => 'Updated message',
            'inbound' => false,
            'outbound' => true,
        ]);
    }

    public function test_can_delete_exchanged_message()
    {
        $exchangedMessage = ExchangedMessage::factory()->create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
        ]);

        $response = $this->deleteJson("/api/exchanged_messages/{$exchangedMessage->id}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('exchanged_messages', [
            'id' => $exchangedMessage->id,
        ]);
    }

    public function test_cannot_access_other_organization_messages()
    {
        $otherOrganization = Organization::factory()->create();
        $otherClient = Client::factory()->create([
            'organization_id' => $otherOrganization->id
        ]);

        $exchangedMessage = ExchangedMessage::factory()->create([
            'organization_id' => $otherOrganization->id,
            'client_id' => $otherClient->id,
        ]);

        $response = $this->getJson("/api/exchanged_messages/{$exchangedMessage->id}");

        $response->assertStatus(404);
    }
}
