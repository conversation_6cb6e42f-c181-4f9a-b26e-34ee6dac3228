<?php

namespace Tests\Feature\ChatBot\ExchangedMessage;

use App\Models\Client;
use App\Models\ExchangedMessage;
use App\Models\Organization;
use App\Models\PhoneNumber;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ChatByClientEndpointTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;
    private Client $client;
    private PhoneNumber $phoneNumber;

    protected function setUp(): void
    {
        parent::setUp();

        $this->organization = Organization::factory()->create();
        $this->user = User::factory()->create(['organization_id' => $this->organization->id]);
        $this->client = Client::factory()->create(['organization_id' => $this->organization->id]);
        $this->phoneNumber = PhoneNumber::factory()->create(['organization_id' => $this->organization->id]);
    }

    public function test_should_return_unauthorized_when_not_authenticated()
    {
        $response = $this->getJson("/api/exchanged_messages/chat-by-client/{$this->client->id}");

        $response->assertStatus(401);
    }

    public function test_should_return_error_when_client_not_found()
    {
        $response = $this->actingAs($this->user)
            ->getJson("/api/exchanged_messages/chat-by-client/999");

        $response->assertStatus(400)
            ->assertJson([
                'status' => 'error',
                'message' => 'Client not found'
            ]);
    }

    public function test_should_return_error_when_client_from_different_organization()
    {
        $otherOrganization = Organization::factory()->create();
        $otherClient = Client::factory()->create(['organization_id' => $otherOrganization->id]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/exchanged_messages/chat-by-client/{$otherClient->id}");

        $response->assertStatus(400)
            ->assertJson([
                'status' => 'error',
                'message' => 'Client does not belong to organization'
            ]);
    }

    public function test_should_return_empty_chat_when_no_messages()
    {
        $response = $this->actingAs($this->user)
            ->getJson("/api/exchanged_messages/chat-by-client/{$this->client->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Chat history retrieved successfully',
                'data' => [],
                'pagination' => [
                    'count' => 0,
                    'client_id' => $this->client->id,
                    'organization_id' => $this->organization->id,
                    'limit' => 50
                ]
            ]);
    }

    public function test_should_return_chat_history_ordered_by_sent_at_asc()
    {
        // Criar mensagens com timestamps diferentes
        $message1 = ExchangedMessage::create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'inbound' => true,
            'outbound' => false,
            'message' => 'Primeira mensagem',
            'sent_at' => Carbon::now()->subMinutes(10)
        ]);

        $message2 = ExchangedMessage::create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'inbound' => false,
            'outbound' => true,
            'message' => 'Segunda mensagem',
            'sent_at' => Carbon::now()->subMinutes(5)
        ]);

        $message3 = ExchangedMessage::create([
            'organization_id' => $this->organization->id,
            'client_id' => $this->client->id,
            'phone_number_id' => $this->phoneNumber->id,
            'inbound' => true,
            'outbound' => false,
            'message' => 'Terceira mensagem',
            'sent_at' => Carbon::now()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/exchanged_messages/chat-by-client/{$this->client->id}");

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Chat history retrieved successfully',
                'pagination' => [
                    'count' => 3,
                    'client_id' => $this->client->id,
                    'organization_id' => $this->organization->id,
                    'limit' => 50
                ]
            ]);

        $data = $response->json('data');

        // Verificar se as mensagens estão ordenadas por sent_at ASC (mais antigas primeiro)
        $this->assertCount(3, $data);
        $this->assertEquals('Primeira mensagem', $data[0]['message']);
        $this->assertEquals('Segunda mensagem', $data[1]['message']);
        $this->assertEquals('Terceira mensagem', $data[2]['message']);

        // Verificar se os timestamps estão em ordem crescente
        $this->assertLessThan($data[1]['sent_at'], $data[0]['sent_at']);
        $this->assertLessThan($data[2]['sent_at'], $data[1]['sent_at']);
    }

    public function test_should_respect_custom_limit()
    {
        // Criar 5 mensagens
        for ($i = 1; $i <= 5; $i++) {
            ExchangedMessage::create([
                'organization_id' => $this->organization->id,
                'client_id' => $this->client->id,
                'phone_number_id' => $this->phoneNumber->id,
                'inbound' => true,
                'outbound' => false,
                'message' => "Mensagem {$i}",
                'sent_at' => Carbon::now()->subMinutes(10 - $i)
            ]);
        }

        $response = $this->actingAs($this->user)
            ->getJson("/api/exchanged_messages/chat-by-client/{$this->client->id}?limit=3");

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'pagination' => [
                    'count' => 3,
                    'limit' => 3
                ]
            ]);

        $data = $response->json('data');
        $this->assertCount(3, $data);

        // Deve retornar as 3 mais recentes, mas ordenadas em ASC
        $this->assertEquals('Mensagem 3', $data[0]['message']);
        $this->assertEquals('Mensagem 4', $data[1]['message']);
        $this->assertEquals('Mensagem 5', $data[2]['message']);
    }

    public function test_should_limit_maximum_to_100_messages()
    {
        $response = $this->actingAs($this->user)
            ->getJson("/api/exchanged_messages/chat-by-client/{$this->client->id}?limit=200");

        $response->assertStatus(200)
            ->assertJson([
                'pagination' => [
                    'limit' => 100
                ]
            ]);
    }
}
