<?php

namespace Tests\Feature\Services\OpenAI;

use App\Services\OpenAI\OpenAIService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class OpenAIServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_instantiate_openai_service()
    {
        $service = app()->make(OpenAIService::class);
        $this->assertInstanceOf(OpenAIService::class, $service);
    }

    public function test_analyze_image_with_mock_response()
    {
        // Mock successful OpenAI API response with new structure
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => json_encode([
                                'invoice' => [
                                    'number' => 'NF-001234',
                                    'value' => 1500.50,
                                    'amount_received' => 1500.50,
                                    'observation' => 'Cirurgia realizada com sucesso',
                                    'honorary' => 800.00,
                                    'origin' => 'Hospital São Lucas',
                                    'status' => 'Pago',
                                    'date_payment' => '20/12/2024',
                                    'video_fee' => null,
                                    'send_date' => '20/12/2024',
                                    'paid' => true,
                                    'filepath' => null,
                                    'nameFile' => null,
                                    'client_id' => null,
                                    'type_receipt_id' => null,
                                    'type_participation_id' => null,
                                    'hospital_id' => null,
                                    'hospital' => [
                                        'name' => 'Hospital São Lucas',
                                        'desc' => 'Hospital de referência'
                                    ],
                                    'user_id' => null,
                                    'user' => [
                                        'name' => 'Dr. Maria Oliveira',
                                        'desc' => 'Cirurgiã especialista'
                                    ],
                                    'business_id' => null,
                                    'business' => [
                                        'name' => 'Clínica Médica ABC',
                                        'desc' => 'Clínica especializada'
                                    ],
                                    'agreement_id' => null,
                                    'account_id' => null,
                                    'account' => [
                                        'name' => 'Conta Principal',
                                        'desc' => 'Conta de faturamento'
                                    ],
                                    'reason_glosa_id' => null,
                                    'reason_glosa' => [
                                        'name' => null,
                                        'desc' => null
                                    ],
                                    'number_note' => 'NF-001234'
                                ],
                                'client' => [
                                    'name' => 'João Silva Santos',
                                    'email' => null,
                                    'phone' => null,
                                    'cpf' => '123.456.789-00',
                                    'rg' => null,
                                    'number_agreements' => '********',
                                    'agreement_id' => null,
                                    'creator_id' => null,
                                    'creator' => [
                                        'name' => 'Sistema Telegram',
                                        'desc' => 'Criado via bot'
                                    ],
                                    'account_id' => null,
                                    'account' => [
                                        'name' => 'Conta Paciente',
                                        'desc' => 'Conta do paciente'
                                    ]
                                ],
                                'agreement' => [
                                    'name' => 'Unimed',
                                    'url' => null,
                                    'observation' => null
                                ],
                                'proceeding' => [
                                    'desc' => 'Cirurgia de vesícula',
                                    'code' => 'CIR-001',
                                    'status' => 'Realizado',
                                    'size' => 'Médio',
                                    'operational_cost' => 700.50,
                                    'number_assistants' => '2',
                                    'size_anesthetist' => 'Médio'
                                ],
                                'raw_information' => [
                                    'paciente' => 'João Silva Santos',
                                    'data_nascimento' => '15/03/1980',
                                    'convenio' => 'Unimed',
                                    'data_inicio' => '20/12/2024',
                                    'cirurgiao' => 'Dr. Maria Oliveira',
                                    'procedimento_descricao' => 'Cirurgia de vesícula',
                                    'diagnostico_pre_operatorio' => 'Colelitíase'
                                ],
                                '_token' => '7438923:jzhdsifhosdihfasdadsf8jxzcoa'
                            ])
                        ]
                    ]
                ],
                'usage' => [
                    'prompt_tokens' => 100,
                    'completion_tokens' => 50,
                    'total_tokens' => 150
                ],
                'model' => 'gpt-4-vision-preview'
            ], 200)
        ]);

        // Create a test image file
        $testImagePath = storage_path('app/test_image.jpg');
        file_put_contents($testImagePath, base64_decode('/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/wA=='));

        $service = new OpenAIService();
        $result = $service->analyzeImage($testImagePath);

        $this->assertEquals('OpenAI ChatGPT', $result['source']);
        $this->assertEquals('success', $result['status']);
        $this->assertArrayHasKey('data', $result);

        // Test new structure
        $this->assertArrayHasKey('invoice', $result['data']);
        $this->assertArrayHasKey('client', $result['data']);
        $this->assertArrayHasKey('agreement', $result['data']);
        $this->assertArrayHasKey('proceeding', $result['data']);
        $this->assertArrayHasKey('raw_information', $result['data']);
        $this->assertArrayHasKey('_token', $result['data']);

        // Test specific values
        $this->assertEquals('João Silva Santos', $result['data']['client']['name']);
        $this->assertEquals('Unimed', $result['data']['agreement']['name']);
        $this->assertEquals('Cirurgia de vesícula', $result['data']['proceeding']['desc']);
        $this->assertEquals('João Silva Santos', $result['data']['raw_information']['paciente']);

        // Test token
        $this->assertEquals('7438923:jzhdsifhosdihfasdadsf8jxzcoa', $result['data']['_token']);

        // Test subjsons
        $this->assertArrayHasKey('hospital', $result['data']['invoice']);
        $this->assertArrayHasKey('user', $result['data']['invoice']);
        $this->assertArrayHasKey('business', $result['data']['invoice']);
        $this->assertArrayHasKey('account', $result['data']['invoice']);
        $this->assertArrayHasKey('reason_glosa', $result['data']['invoice']);

        $this->assertArrayHasKey('creator', $result['data']['client']);
        $this->assertArrayHasKey('account', $result['data']['client']);

        // Test subjson values
        $this->assertEquals('Hospital São Lucas', $result['data']['invoice']['hospital']['name']);
        $this->assertEquals('Dr. Maria Oliveira', $result['data']['invoice']['user']['name']);
        $this->assertEquals('Sistema Telegram', $result['data']['client']['creator']['name']);

        // Clean up
        unlink($testImagePath);
    }

    public function test_analyze_image_throws_exception_for_missing_file()
    {
        $service = new OpenAIService();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Image file not found at:');

        $service->analyzeImage('/path/to/nonexistent/file.jpg');
    }

    public function test_analyze_image_handles_api_error()
    {
        // Mock API error response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'error' => [
                    'message' => 'Invalid API key',
                    'type' => 'invalid_request_error'
                ]
            ], 401)
        ]);

        // Create a test image file
        $testImagePath = storage_path('app/test_image.jpg');
        file_put_contents($testImagePath, 'fake image content');

        $service = new OpenAIService();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('OpenAI API error:');

        $service->analyzeImage($testImagePath);

        // Clean up
        unlink($testImagePath);
    }

    public function test_analyze_image_handles_invalid_json_response()
    {
        // Mock response with invalid JSON
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'content' => 'This is not valid JSON content'
                        ]
                    ]
                ]
            ], 200)
        ]);

        // Create a test image file
        $testImagePath = storage_path('app/test_image.jpg');
        file_put_contents($testImagePath, 'fake image content');

        $service = new OpenAIService();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to parse JSON from OpenAI response');

        $service->analyzeImage($testImagePath);

        // Clean up
        unlink($testImagePath);
    }
}
