<?php

namespace App\Services\Telegram\UseCases\Flows\ReadDocText;

use App\Helpers\DBLog;
use App\Services\Telegram\Telegram;
use Illuminate\Support\Facades\Http;

class SendDocumentToWebhook
{

    public Telegram $telegram;

    public const string WEBHOOK_URL = "https://api.billdoctor.com.br/api/webhook-create";
    public const string WEBHOOK_TOKEN = "7438923:jzhdsifhosdihfasdadsf8jxzcoa";

    public function __construct(Telegram $telegram){
        $this->telegram = $telegram;
    }

    public function perform($data): void {
        DBLog::log(
            "SendDocumentToWebhook::perform - Raw data received",
            "Telegram::SendDocumentToWebhook" ,
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['raw_data' => $data]
        );

        try {
            // Clean and normalize the data
            $cleanedData = $this->cleanJsonData($data);

            DBLog::log(
                "SendDocumentToWebhook::perform - Cleaned data",
                "Telegram::SendDocumentToWebhook" ,
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ['cleaned_data' => $cleanedData]
            );

            // Parse the cleaned data
            $parsedData = json_decode($cleanedData, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('JSON parsing error: ' . json_last_error_msg());
            }

            // Add webhook token
            $parsedData['_token'] = self::WEBHOOK_TOKEN;

            DBLog::log(
                "SendDocumentToWebhook::perform - Final data to send",
                "Telegram::SendDocumentToWebhook" ,
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                $parsedData
            );

            // Send to webhook
            $response = Http::post(self::WEBHOOK_URL, $parsedData);

            DBLog::log(
                "SendDocumentToWebhook::perform - Webhook response",
                "Telegram" ,
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]
            );

        } catch (\Exception $e) {
            DBLog::logError(
                "SendDocumentToWebhook::perform - Failed to send webhook",
                "Telegram",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                [
                    'error' => $e->getMessage(),
                    'raw_data' => $data,
                    'line' => $e->getLine(),
                    'file' => $e->getFile()
                ]
            );
        }
    }

    /**
     * Clean and normalize JSON data that may have been encoded multiple times
     */
    private function cleanJsonData($data): string
    {
        // If data is null or empty, return empty JSON object
        if (empty($data)) {
            return '{}';
        }

        // If data is already an array, encode it properly
        if (is_array($data)) {
            return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }

        // Convert to string if not already
        $dataString = (string) $data;

        // Handle double-encoded JSON (common issue with ChatGPT data)
        if (is_string($data) && strpos($data, '"{') === 0) {
            // This looks like a double-encoded JSON string
            $decoded = json_decode($data, true);
            if (json_last_error() === JSON_ERROR_NONE && is_string($decoded)) {
                // Successfully decoded the outer layer, now decode the inner JSON
                $dataString = $decoded;

                DBLog::log(
                    "cleanJsonData - Fixed double-encoded JSON",
                    "Telegram",
                    request()->user()->organization_id ?? null,
                    request()->user()->id ?? null,
                    ['fixed_data' => substr($dataString, 0, 500) . '...']
                );
            }
        }

        // Log the original data for debugging
        DBLog::log(
            "cleanJsonData - Original data",
            "Telegram",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['original' => substr($dataString, 0, 500) . '...', 'length' => strlen($dataString)]
        );

        // Detect and handle different data formats
        $cleaned = $this->detectAndNormalizeFormat($dataString);

        // Try to decode and validate
        $decoded = json_decode($cleaned, true);

        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $result = json_encode($decoded, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            DBLog::log(
                "cleanJsonData - Successfully cleaned",
                "Telegram",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ['cleaned' => substr($result, 0, 500) . '...', 'length' => strlen($result)]
            );

            return $result;
        }

        // Log parsing error for debugging
        DBLog::logError(
            "cleanJsonData - JSON parsing failed: " . json_last_error_msg(),
            "Telegram",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['data' => substr($cleaned, 0, 500) . '...', 'error' => json_last_error_msg()]
        );

        // Return empty object if all cleaning attempts fail
        return '{}';
    }

    /**
     * Detect data format and apply appropriate normalization
     */
    private function detectAndNormalizeFormat(string $data): string
    {
        // Check if data contains raw_data structure (from Telegram)
        if (strpos($data, '"raw_data"') !== false) {
            return $this->normalizeTelegramFormat($data);
        }

        // Check if data is from database (heavily escaped)
        if (preg_match('/\\\\\\\\\\\\/', $data)) {
            return $this->normalizeDatabaseFormat($data);
        }

        // Default normalization
        return $this->normalizeJsonString($data);
    }

    /**
     * Normalize Telegram format data to match database format
     */
    private function normalizeTelegramFormat(string $data): string
    {
        DBLog::log(
            "normalizeTelegramFormat - Processing Telegram data",
            "Telegram",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['data_preview' => substr($data, 0, 200) . '...']
        );

        // First, try to extract the raw_data content
        $decoded = json_decode($data, true);
        if (isset($decoded['raw_data'])) {
            $rawData = $decoded['raw_data'];

            DBLog::log(
                "normalizeTelegramFormat - Extracted raw_data",
                "Telegram",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ['raw_data_preview' => substr($rawData, 0, 300) . '...']
            );

            // Step 1: Remove formatting characters
            $cleaned = $rawData;

            // Remove line breaks (both literal and escaped)
            $cleaned = str_replace(['\n', '\\n', '\r', '\\r'], '', $cleaned);

            // Remove non-breaking spaces (both Unicode and escaped)
            $cleaned = str_replace(['\u00a0', '\\u00a0'], '', $cleaned);

            // Remove all whitespace between JSON elements (but preserve spaces in values)
            $cleaned = preg_replace('/(?<=[{,:])\s+(?=["\[\{])/', '', $cleaned);
            $cleaned = preg_replace('/(?<=["\]\}])\s+(?=[,\]\}])/', '', $cleaned);

            DBLog::log(
                "normalizeTelegramFormat - After whitespace removal",
                "Telegram",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ['cleaned_preview' => substr($cleaned, 0, 300) . '...']
            );

            // Step 2: Fix escaped characters
            $cleaned = str_replace('\\"', '"', $cleaned);
            $cleaned = str_replace('\\/', '/', $cleaned);

            // Step 3: Try to decode and validate
            $innerDecoded = json_decode($cleaned, true);

            if (json_last_error() === JSON_ERROR_NONE && is_array($innerDecoded)) {
                // Success! Re-encode in database format
                $normalized = json_encode($innerDecoded, JSON_UNESCAPED_SLASHES);

                DBLog::log(
                    "normalizeTelegramFormat - Successfully normalized",
                    "Telegram",
                    request()->user()->organization_id ?? null,
                    request()->user()->id ?? null,
                    [
                        'success' => true,
                        'normalized_preview' => substr($normalized, 0, 300) . '...',
                        'patient' => $innerDecoded['paciente'] ?? 'N/A'
                    ]
                );

                return $normalized;
            } else {
                // Log the JSON error for debugging
                DBLog::logError(
                    "normalizeTelegramFormat - JSON decode failed: " . json_last_error_msg(),
                    "Telegram",
                    request()->user()->organization_id ?? null,
                    request()->user()->id ?? null,
                    [
                        'cleaned_data' => substr($cleaned, 0, 500) . '...',
                        'json_error' => json_last_error_msg(),
                        'json_error_code' => json_last_error()
                    ]
                );

                // Try alternative cleaning approach
                return $this->alternativeTelegramCleaning($rawData);
            }
        }

        // Fallback to default normalization
        return $this->normalizeJsonString($data);
    }

    /**
     * Alternative cleaning method for problematic Telegram data
     */
    private function alternativeTelegramCleaning(string $rawData): string
    {
        DBLog::log(
            "alternativeTelegramCleaning - Trying alternative approach",
            "Telegram",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['raw_data_preview' => substr($rawData, 0, 200) . '...']
        );

        $cleaned = $rawData;

        // More aggressive cleaning
        // Remove all types of line breaks and spaces
        $cleaned = preg_replace('/\\\\[nr]/', '', $cleaned);
        $cleaned = preg_replace('/\\\\u00a0/', '', $cleaned);
        $cleaned = str_replace(['\n', '\r', '\t'], '', $cleaned);

        // Remove extra whitespace more aggressively
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = str_replace(' "', '"', $cleaned);
        $cleaned = str_replace('" ', '"', $cleaned);
        $cleaned = str_replace(' :', ':', $cleaned);
        $cleaned = str_replace(': ', ':', $cleaned);
        $cleaned = str_replace(' ,', ',', $cleaned);
        $cleaned = str_replace(', ', ',', $cleaned);
        $cleaned = str_replace(' {', '{', $cleaned);
        $cleaned = str_replace('{ ', '{', $cleaned);
        $cleaned = str_replace(' }', '}', $cleaned);
        $cleaned = str_replace('} ', '}', $cleaned);
        $cleaned = str_replace(' [', '[', $cleaned);
        $cleaned = str_replace('[ ', '[', $cleaned);
        $cleaned = str_replace(' ]', ']', $cleaned);
        $cleaned = str_replace('] ', ']', $cleaned);

        // Fix escaped characters
        $cleaned = str_replace('\\"', '"', $cleaned);
        $cleaned = str_replace('\\/', '/', $cleaned);

        // Try to decode
        $decoded = json_decode($cleaned, true);

        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            $result = json_encode($decoded, JSON_UNESCAPED_SLASHES);

            DBLog::log(
                "alternativeTelegramCleaning - Success with alternative method",
                "Telegram",
                request()->user()->organization_id ?? null,
                request()->user()->id ?? null,
                ['result_preview' => substr($result, 0, 200) . '...']
            );

            return $result;
        }

        // If still failing, return empty object
        DBLog::logError(
            "alternativeTelegramCleaning - All methods failed",
            "Telegram",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['final_cleaned' => substr($cleaned, 0, 500) . '...']
        );

        return '{}';
    }

    /**
     * Normalize database format data
     */
    private function normalizeDatabaseFormat(string $data): string
    {
        DBLog::log(
            "normalizeDatabaseFormat - Processing database data",
            "Telegram",
            request()->user()->organization_id ?? null,
            request()->user()->id ?? null,
            ['data_preview' => substr($data, 0, 200) . '...']
        );

        $cleaned = $data;

        // Remove outer quotes if present
        if (preg_match('/^".*"$/', $cleaned)) {
            $cleaned = trim($cleaned, '"');
        }

        // Fix multiple levels of escaping
        $cleaned = str_replace(['\\\\\\/', '\\\\/'], '/', $cleaned);
        $cleaned = str_replace(['\\"', '\\\\'], ['"', '\\'], $cleaned);

        // Handle Unicode escapes
        $cleaned = preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function($matches) {
            return json_decode('"\\u' . $matches[1] . '"');
        }, $cleaned);

        return $cleaned;
    }

    /**
     * Normalize JSON string by handling various encoding issues
     */
    private function normalizeJsonString(string $data): string
    {
        $cleaned = $data;

        // Remove outer quotes if the entire string is wrapped in quotes
        if (preg_match('/^".*"$/', $cleaned)) {
            $cleaned = trim($cleaned, '"');
        }

        // Handle callback_data structure from Telegram
        if (strpos($cleaned, 'callback_data') !== false) {
            $tempDecoded = json_decode($cleaned, true);
            if (isset($tempDecoded['callback_data'])) {
                $cleaned = $tempDecoded['callback_data'];
            }
        }

        // Replace non-breaking spaces with regular spaces
        $cleaned = str_replace(['\u00a0', '\\u00a0'], ' ', $cleaned);

        // Fix escaped forward slashes
        $cleaned = str_replace('\/', '/', $cleaned);

        // Fix double-escaped quotes
        $cleaned = str_replace('\\"', '"', $cleaned);

        // Remove extra whitespace and newlines that might cause issues
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = str_replace(['\n', '\r', '\t'], ' ', $cleaned);

        // Try progressive unescaping
        $attempts = 0;
        $maxAttempts = 3;

        while ($attempts < $maxAttempts) {
            $testDecode = json_decode($cleaned, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($testDecode)) {
                break;
            }

            // Try unescaping one more level
            $cleaned = stripslashes($cleaned);
            $attempts++;
        }

        return $cleaned;
    }

    /**
     * Alternative method specifically for data coming from database
     */
    public function cleanDatabaseJsonData($data): string
    {
        if (empty($data)) {
            return '{}';
        }

        // Database data often comes with extra escaping
        $cleaned = $data;

        // Remove outer quotes if present
        if (preg_match('/^".*"$/', $cleaned)) {
            $cleaned = trim($cleaned, '"');
        }

        // Fix common database escaping issues
        $cleaned = str_replace(['\\/', '\\"', '\\\\'], ['/', '"', '\\'], $cleaned);

        // Handle Unicode escapes
        $cleaned = preg_replace_callback('/\\\\u([0-9a-fA-F]{4})/', function($matches) {
            return mb_convert_encoding(pack('H*', $matches[1]), 'UTF-8', 'UCS-2BE');
        }, $cleaned);

        // Validate and return
        $decoded = json_decode($cleaned, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return json_encode($decoded, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }

        return '{}';
    }

}
