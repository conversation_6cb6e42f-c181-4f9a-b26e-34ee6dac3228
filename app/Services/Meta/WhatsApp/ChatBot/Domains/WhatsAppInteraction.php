<?php

namespace App\Services\Meta\WhatsApp\ChatBot\Domains;

use App\Domains\ChatBot\Interaction;
use Carbon\Carbon;

class WhatsAppInteraction extends Interaction
{
    public ?string $whatsapp_message_id;
    public ?string $whatsapp_message_type;
    public ?array $whatsapp_raw_data;

    public function __construct(
        ?int $id = null,
        ?int $organization_id = null,
        ?int $user_id = null,
        ?int $client_id = null,
        ?int $flow_id = null,
        ?int $step_id = null,
        ?int $conversation_id = null,
        ?string $message = null,
        ?string $answer = null,
        ?string $result = null,
        ?string $json = null,
        ?Carbon $created_at = null,
        ?Carbon $updated_at = null,
        ?string $whatsapp_message_id = null,
        ?string $whatsapp_message_type = null,
        ?array $whatsapp_raw_data = null
    ) {
        parent::__construct(
            $id,
            $organization_id,
            $user_id,
            $client_id,
            $flow_id,
            $step_id,
            $conversation_id,
            $message,
            $answer,
            $result,
            $json,
            $created_at,
            $updated_at
        );

        $this->whatsapp_message_id = $whatsapp_message_id;
        $this->whatsapp_message_type = $whatsapp_message_type;
        $this->whatsapp_raw_data = $whatsapp_raw_data;
    }

    /**
     * Check if this is a button interaction
     */
    public function isButtonInteraction(): bool
    {
        return $this->whatsapp_message_type === 'interactive' &&
               isset($this->whatsapp_raw_data['interactive']['type']) &&
               $this->whatsapp_raw_data['interactive']['type'] === 'button_reply';
    }

    /**
     * Check if this is a list interaction
     */
    public function isListInteraction(): bool
    {
        return $this->whatsapp_message_type === 'interactive' &&
               isset($this->whatsapp_raw_data['interactive']['type']) &&
               $this->whatsapp_raw_data['interactive']['type'] === 'list_reply';
    }

    /**
     * Check if this is a text message
     */
    public function isTextMessage(): bool
    {
        return $this->whatsapp_message_type === 'text';
    }

    /**
     * Get button ID from interaction
     */
    public function getButtonId(): ?string
    {
        if (!$this->isButtonInteraction()) {
            return null;
        }

        return $this->whatsapp_raw_data['interactive']['button_reply']['id'] ?? null;
    }

    /**
     * Get list selection ID from interaction
     */
    public function getListSelectionId(): ?string
    {
        if (!$this->isListInteraction()) {
            return null;
        }

        return $this->whatsapp_raw_data['interactive']['list_reply']['id'] ?? null;
    }

    /**
     * Get text content from message
     */
    public function getTextContent(): ?string
    {
        if ($this->isTextMessage()) {
            return $this->whatsapp_raw_data['text']['body'] ?? null;
        }

        return $this->message;
    }

    /**
     * Get input text (alias for getTextContent for compatibility)
     */
    public function getInputText(): ?string
    {
        return $this->getTextContent();
    }

    /**
     * Magic getter for input_text property
     */
    public function __get($property)
    {
        if ($property === 'input_text') {
            return $this->getInputText();
        }

        if ($property === 'message_text') {
            return $this->message;
        }

        return parent::__get($property) ?? null;
    }

    public function toArray(): array
    {
        $baseArray = parent::toArray();

        return array_merge($baseArray, [
            'whatsapp_message_id' => $this->whatsapp_message_id,
            'whatsapp_message_type' => $this->whatsapp_message_type,
            'whatsapp_raw_data' => $this->whatsapp_raw_data,
        ]);
    }

    public function toStoreArray(): array
    {
        $baseArray = parent::toStoreArray();

        // Store WhatsApp data in the JSON field
        $whatsappData = [
            'whatsapp_message_id' => $this->whatsapp_message_id,
            'whatsapp_message_type' => $this->whatsapp_message_type,
            'whatsapp_raw_data' => $this->whatsapp_raw_data,
        ];

        $baseArray['json'] = json_encode($whatsappData);

        return $baseArray;
    }
}
