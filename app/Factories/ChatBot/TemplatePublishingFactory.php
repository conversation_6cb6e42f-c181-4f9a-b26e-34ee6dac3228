<?php

namespace App\Factories\ChatBot;

use App\Domains\ChatBot\Template;
use App\Domains\ChatBot\TemplatePublishing;
use App\Enums\PublishingService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\TemplatePublishing as TemplatePublishingModel;

class TemplatePublishingFactory
{
    /**
     * Create domain from HTTP store request
     */
    public function buildFromRequest(Request $request): TemplatePublishing
    {
        return new TemplatePublishing(
            null,
            $request->input('template_id'),
            PublishingService::from((int) $request->input('service_id')),
            (bool) $request->input('is_queued', false),
            (bool) $request->input('is_published', false),
            $request->input('status') ?? null,
            $request->input('published_at') ? new \DateTime($request->input('published_at')) : null
        );
    }

    /**
     * Build from TemplatePublishingModel model
     */
    public function buildFromModel(?TemplatePublishingModel $model): ?TemplatePublishing
    {
        if (!$model) { return null; }

        return new TemplatePublishing(
            $model->id ?? null,
            $model->template_id ?? null,
            $model->service_id ?? null,
            $model->is_queued ?? null,
            $model->is_published ?? null,
            $model->status ?? null,
            $model->published_at ?? null,
            $model->created_at ?? null,
            $model->updated_at ?? null,
        );
    }

    public function buildFromTemplatePublish(int $template_id, PublishingService $service): TemplatePublishing {
        return new TemplatePublishing(
            null,
            $template_id,
            $service,
            true,
            false,
            "to_publish",
            Carbon::now()
        );
    }

}
