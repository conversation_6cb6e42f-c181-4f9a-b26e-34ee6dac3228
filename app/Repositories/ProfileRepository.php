<?php

namespace App\Repositories;

use App\Domains\Profile as ProfileDomain;
use App\Factories\ProfileFactory;
use App\Models\Profile;

class ProfileRepository
{
    private ProfileFactory $profileFactory;

    public function __construct(ProfileFactory $profileFactory){
        $this->profileFactory = $profileFactory;
    }

    /**
     * @return ProfileDomain[]
     */
    public function fetchAll() : array {
        $profilees = [];

        $models = Profile::all();

        foreach ($models as $model){
            $profilees[] = $this->profileFactory->buildFromModel($model);
        }

        return $profilees;
    }

    public function store(ProfileDomain $profile) : ProfileDomain {
        $savedProfile = Profile::create($profile->toStoreArray());

        $profile->id = $savedProfile->id;

        return $profile;
    }

    public function update(ProfileDomain $profile) : ProfileDomain {
        Profile::where('id', $profile->id)
            ->update($profile->toUpdateArray());

        return $profile;
    }

    public function fetchById(int $id) : ProfileDomain {
        return $this->profileFactory->buildFromModel(
            Profile::findOrFail($id)
        );
    }

    public function delete(ProfileDomain $profile) : bool {
        return Profile::find($profile->id)->delete();
    }
}
