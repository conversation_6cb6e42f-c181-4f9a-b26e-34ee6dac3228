<?php

namespace App\Repositories;

use App\Domains\ChatBot\Category as CategoryDomain;
use App\Factories\ChatBot\CategoryFactory;
use App\Models\Category;
use EloquentBuilder;

class CategoryRepository
{
    private CategoryFactory $categoryFactory;

    public function __construct(CategoryFactory $categoryFactory)
    {
        $this->categoryFactory = $categoryFactory;
    }

    public function fetchAll(int $organization_id, ?string $type = null): array
    {
        $query = Category::where('organization_id', $organization_id)
                        ->orderBy('name');

        if ($type) {
            $query->where('type', $type);
        }

        return $this->categoryFactory->buildCollection($query->get());
    }

    public function fetchById(int $id, int $organization_id): CategoryDomain
    {
        $model = Category::where('id', $id)
                        ->where('organization_id', $organization_id)
                        ->firstOrFail();

        return $this->categoryFactory->buildFromModel($model);
    }

    public function fetchByName(string $name, string $type, int $organization_id): ?CategoryDomain
    {
        $model = Category::where('name', $name)
                        ->where('type', $type)
                        ->where('organization_id', $organization_id)
                        ->first();

        return $model ? $this->categoryFactory->buildFromModel($model) : null;
    }

    public function store(CategoryDomain $category): CategoryDomain
    {
        $model = Category::create($category->toStoreArray());
        $category->id = $model->id;
        $category->created_at = $model->created_at;
        $category->updated_at = $model->updated_at;

        return $category;
    }

    public function update(CategoryDomain $category, int $organization_id): CategoryDomain
    {
        Category::where('id', $category->id)
               ->where('organization_id', $organization_id)
               ->update($category->toUpdateArray());

        return $category;
    }

    public function delete(int $id, int $organization_id): bool
    {
        return Category::where('id', $id)
                      ->where('organization_id', $organization_id)
                      ->delete() > 0;
    }

    public function exists(string $name, string $type, int $organization_id, ?int $excludeId = null): bool
    {
        $query = Category::where('name', $name)
                        ->where('type', $type)
                        ->where('organization_id', $organization_id);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    public function getUsageCount(int $id): int
    {
        $category = Category::withCount('campaigns')->find($id);
        return $category ? $category->campaigns_count : 0;
    }

    public function search(int $organization_id, string $query, ?string $type = null): array
    {
        $queryBuilder = Category::where('organization_id', $organization_id)
                               ->where('name', 'LIKE', "%{$query}%")
                               ->orderBy('name');

        if ($type) {
            $queryBuilder->where('type', $type);
        }

        return $this->categoryFactory->buildCollection($queryBuilder->get());
    }
}
