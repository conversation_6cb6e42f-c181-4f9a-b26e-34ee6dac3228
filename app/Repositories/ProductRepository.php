<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProductFilters;
use App\Domains\Inventory\Product as ProductDomain;
use App\Factories\Inventory\ProductFactory;
use App\Models\Product;
use EloquentBuilder;

class ProductRepository
{
    private ProductFactory $productFactory;

    public function __construct(ProductFactory $productFactory){
        $this->productFactory = $productFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ProductFilters $filters, OrderBy $orderBy) : array {
        $products = [];

        $models = EloquentBuilder::to(Product::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $products[] = $this->productFactory->buildFromModel($model);
        }

        return [
            'data' => $products,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ProductFilters $filters, OrderBy $orderBy) : array {
        $products = [];

        $models = EloquentBuilder::to(Product::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $products[] = $this->productFactory->buildFromModel($model);
        }

        return [
            'data' => $products,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ProductFilters $filters): int {
        return EloquentBuilder::to(Product::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, ProductFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Product::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(ProductDomain $product) : ProductDomain {
        $savedProduct = Product::create($product->toStoreArray());

        $product->id = $savedProduct->id;

        return $product;
    }

    public function update(ProductDomain $product, int $organization_id) : ProductDomain {
        Product::where('id', $product->id)
            ->where('organization_id', $organization_id)
            ->update($product->toUpdateArray());

        return $product;
    }

    public function fetchById(int $id) : ProductDomain {
        return $this->productFactory->buildFromModel(
            Product::findOrFail($id)
        );
    }

    public function delete(ProductDomain $product) : bool {
        return Product::find($product->id)->delete();
    }
}
