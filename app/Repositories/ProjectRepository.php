<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\ProjectFilters;
use App\Domains\Inventory\ProductsAttachs\AttachCustomDomain;
use App\Domains\Inventory\ProductsAttachs\AttachProductsDomain;
use App\Domains\Inventory\Project as ProjectDomain;
use App\Factories\Inventory\ProjectFactory;
use App\Models\Project;
use EloquentBuilder;

class ProjectRepository
{
    private ProjectFactory $projectFactory;

    public function __construct(ProjectFactory $projectFactory){
        $this->projectFactory = $projectFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ProjectFilters $filters, OrderBy $orderBy) : array {
        $projects = [];

        $models = EloquentBuilder::to(Project::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $projects[] = $this->projectFactory->buildFromModel($model);
        }

        return [
            'data' => $projects,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ProjectFilters $filters, OrderBy $orderBy) : array {
        $projects = [];

        $models = EloquentBuilder::to(Project::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $projects[] = $this->projectFactory->buildFromModel($model);
        }

        return [
            'data' => $projects,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ProjectFilters $filters): int {
        return EloquentBuilder::to(Project::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, ProjectFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Project::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(ProjectDomain $project) : ProjectDomain {
        $savedProject = Project::create($project->toStoreArray());

        $project->id = $savedProject->id;

        return $project;
    }

    public function update(ProjectDomain $project, int $organization_id) : ProjectDomain {
        Project::where('id', $project->id)
            ->where('organization_id', $organization_id)
            ->update($project->toUpdateArray());

        return $project;
    }

    public function fetchById(int $id) : ProjectDomain {
        return $this->projectFactory->buildFromModel(
            Project::with('products')
                ->with('client')
                ->findOrFail($id)
        );
    }

    public function delete(ProjectDomain $project) : bool {
        return Project::find($project->id)->delete();
    }

    public function attachProducts(ProjectDomain $project, AttachProductsDomain $product_ids) {
        Project::find($project->id)->products()->attach($product_ids->products);
    }
    public function attachCustoms(ProjectDomain $project, AttachCustomDomain $custom_ids) {
        Project::find($project->id)->custom_products()->saveMany($custom_ids->products);
    }
    public function clearProducts(ProjectDomain $project) {
        Project::find($project->id)->products()->detach();
    }
    public function clearCustoms(ProjectDomain $project) {
        Project::find($project->id)->custom_products()->delete();
    }

}
