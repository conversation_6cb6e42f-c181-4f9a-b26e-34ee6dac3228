<?php

namespace App\Repositories;

use App\Domains\ChatBot\Parameter as ParameterDomain;
use App\Domains\Filters\ParameterFilters;
use App\Domains\Filters\OrderBy;
use App\Factories\ChatBot\ParameterFactory;
use App\Models\Parameter;
use EloquentBuilder;

class ParameterRepository
{
    private ParameterFactory $parameterFactory;

    public function __construct(ParameterFactory $parameterFactory){
        $this->parameterFactory = $parameterFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(ParameterFilters $filters, OrderBy $orderBy) : array {
        $parameters = [];

        $models = EloquentBuilder::to(Parameter::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $parameters[] = $this->parameterFactory->buildFromModel($model);
        }

        return [
            'data' => $parameters,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, ParameterFilters $filters, OrderBy $orderBy) : array {
        $parameters = [];

        $models = EloquentBuilder::to(Parameter::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $parameters[] = $this->parameterFactory->buildFromModel($model);
        }

        return [
            'data' => $parameters,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, ParameterFilters $filters): int {
        return EloquentBuilder::to(Parameter::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function store(ParameterDomain $parameter) : ParameterDomain {
        $savedParameter = Parameter::create($parameter->toStoreArray());

        $parameter->id = $savedParameter->id;

        return $parameter;
    }

    public function update(ParameterDomain $parameter, int $organization_id) : ParameterDomain {
        Parameter::where('id', $parameter->id)
            ->where('organization_id', $organization_id)
            ->update($parameter->toUpdateArray());

        return $parameter;
    }

    public function save(ParameterDomain $parameter, int $organization_id) : ParameterDomain {
        if ($parameter->id){
            $this->update($parameter, $organization_id);
        }
        return $this->store($parameter);
    }

    public function fetchById(int $id) : ParameterDomain {
        return $this->parameterFactory->buildFromModel(
            Parameter::findOrFail($id)
        );
    }

    public function delete(ParameterDomain $parameter) : bool {
        return Parameter::find($parameter->id)->delete();
    }
}
