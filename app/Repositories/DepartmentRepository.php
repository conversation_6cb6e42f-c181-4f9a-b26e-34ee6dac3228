<?php

namespace App\Repositories;

use App\Domains\Filters\OrderBy;
use App\Domains\Filters\DepartmentFilters;
use App\Domains\Inventory\Department as DepartmentDomain;
use App\Factories\Inventory\DepartmentFactory;
use App\Models\Department;
use EloquentBuilder;

class DepartmentRepository
{
    private DepartmentFactory $departmentFactory;

    public function __construct(DepartmentFactory $departmentFactory){
        $this->departmentFactory = $departmentFactory;
    }

    /**
     * @return array
     */
    public function fetchAll(DepartmentFilters $filters, OrderBy $orderBy) : array {
        $departments = [];

        $models = EloquentBuilder::to(Department::class, $filters->filters)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $departments[] = $this->departmentFactory->buildFromModel($model);
        }

        return [
            'data' => $departments,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id, DepartmentFilters $filters, OrderBy $orderBy) : array {
        $departments = [];

        $models = EloquentBuilder::to(Department::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->orderBy($orderBy->order, $orderBy->by)
            ->paginate($orderBy->limit ?? 30);

        foreach ($models as $model){
            $departments[] = $this->departmentFactory->buildFromModel($model);
        }

        return [
            'data' => $departments,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function count($organization_id, DepartmentFilters $filters): int {
        return EloquentBuilder::to(Department::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->count();
    }

    public function sum($organization_id, DepartmentFilters $filters, string $column): float|int {
        return EloquentBuilder::to(Department::class, $filters->filters)
            ->where("organization_id", $organization_id)
            ->sum($column);
    }

    public function store(DepartmentDomain $department) : DepartmentDomain {
        $savedDepartment = Department::create($department->toStoreArray());

        $department->id = $savedDepartment->id;

        return $department;
    }

    public function update(DepartmentDomain $department, int $organization_id) : DepartmentDomain {
        Department::where('id', $department->id)
            ->where('organization_id', $organization_id)
            ->update($department->toUpdateArray());

        return $department;
    }

    public function fetchById(int $id) : DepartmentDomain {
        return $this->departmentFactory->buildFromModel(
            Department::with('users')
                ->findOrFail($id)
        );
    }

    public function delete(DepartmentDomain $department) : bool {
        return Department::find($department->id)->delete();
    }

}
