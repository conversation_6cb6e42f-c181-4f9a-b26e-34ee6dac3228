<?php

namespace App\Repositories;

use App\Domains\Imports\Import as ImportDomain;
use App\Factories\ImportFactory;
use App\Models\Import;

class ImportRepository
{
    private ImportFactory $importFactory;

    public function __construct(ImportFactory $importFactory){
        $this->importFactory = $importFactory;
    }

    /**
     * @return array
     */
    public function fetchAll() : array {
        $imports = [];

        $models = Import::paginate(30);

        foreach ($models as $model){
            $imports[] = $this->importFactory->buildFromModel($model);
        }

        return [
            'data' => $imports,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    /**
     * @return array
     */
    public function fetchFromOrganization($organization_id) : array {
        $imports = [];

        $models = Import::where("organization_id", $organization_id)->paginate(30);

        foreach ($models as $model){
            $imports[] = $this->importFactory->buildFromModel($model);
        }

        return [
            'data' => $imports,
            'count' => $models->count(),
            'total' => $models->total(),
            'currentPage' => $models->currentPage(),
            'lastPage' => $models->lastPage(),
        ];
    }

    public function store(ImportDomain $import) : ImportDomain {
        $savedImport = Import::create($import->toStoreArray());

        $import->id = $savedImport->id;

        return $import;
    }

    public function update(ImportDomain $import, int $organization_id) : ImportDomain {
        Import::where('id', $import->id)
            ->where('organization_id', $organization_id)
            ->update($import->toUpdateArray());

        return $import;
    }

    public function fetchById(int $id) : ImportDomain {
        return $this->importFactory->buildFromModel(
            Import::findOrFail($id)
        );
    }

    public function delete(ImportDomain $import) : bool {
        return Import::find($import->id)->delete();
    }
}
